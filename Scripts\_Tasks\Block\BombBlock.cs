﻿using System.Collections;
using System.Collections.Generic;
using _Tasks.Interfaces;
using UnityEngine;
using Whatwapp.MergeSolitaire.Game;

namespace _Tasks.Block
{
    public class BombBlock :Whatwapp.MergeSolitaire.Game.Block, ISpecialBlock
    {
        [Header("Bomb Settings")] [SerializeField]
        private float _explosionDelay = 0.5f;

        [SerializeField] private int _explosionRadius = 1;
        [SerializeField] private bool _destroysSelf = true;

        public BlockType Type => BlockType.Bomb;
        public bool CanMerge => false; // Bombs don't merge

        private bool _hasExploded = false;
        private Coroutine _explosionCoroutine;

        public override void Init(BlockValue value, BlockSeed seed)
        {
            base.Init(value, seed);
        }

        public void OnPlacementComplete()
        {
        }

        public void OnMovementStopped()
        {
            if (!_hasExploded)
            {
                _explosionCoroutine = StartCoroutine(ExplodeAfterDelay());
            }
        }

        private IEnumerator ExplodeAfterDelay()
        {
            // Visual countdown or warning effect
            yield return new WaitForSeconds(_explosionDelay);

            if (!_hasExploded)
            {
                Explode();
            }
        }

        private void Explode()
        {
            _hasExploded = true;

            var affectedCells = GetAffectedCells();

            // Collect blocks to destroy
            var blocksToDestroy = new List<Whatwapp.MergeSolitaire.Game.Block>();
            foreach (var cell in affectedCells)
            {
                if (cell.HasBlock && cell.Block != this)
                {
                    blocksToDestroy.Add(cell.Block);
                }
            }

            // Trigger destruction
            if (blocksToDestroy.Count > 0)
            {
                BombEvents.TriggerBlocksDestroyed(blocksToDestroy);
            }

            BombEvents.TriggerExplosion(transform.position, blocksToDestroy.Count);

            // Self-destruct after a brief delay
            StartCoroutine(SelfDestruct());
        }

        private List<Cell> GetAffectedCells()
        {
            var affectedCells = new List<Cell>();
            var board = FindObjectOfType<Board>();
            var currentPos = GetCurrentCellPosition();

            // Get cells in explosion radius
            for (int x = -_explosionRadius; x <= _explosionRadius; x++)
            {
                for (int y = -_explosionRadius; y <= _explosionRadius; y++)
                {
                    var targetPos = currentPos + new Vector2Int(x, y);

                    if (board.IsValidPosition(targetPos.x, targetPos.y))
                    {
                        var cell = board.GetCell(targetPos.x, targetPos.y);
                        affectedCells.Add(cell);
                    }
                }
            }

            return affectedCells;
        }

        private Vector2Int GetCurrentCellPosition()
        {
            var board = FindObjectOfType<Board>();
            return board.GetCellPosition(transform.position);
        }

        private IEnumerator SelfDestruct()
        {
            yield return new WaitForSeconds(0.2f);

            BombEvents.TriggerBombExploded(this, new List<Cell>());

            if (_destroysSelf)
            {
                Remove();
            }
        }

        private void OnDestroy()
        {
            if (_explosionCoroutine != null)
            {
                StopCoroutine(_explosionCoroutine);
            }
        }
    }
}