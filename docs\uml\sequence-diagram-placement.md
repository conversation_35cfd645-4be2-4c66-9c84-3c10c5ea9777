# Block Placement Flow - Sequence Diagram

```mermaid
sequenceDiagram
    participant Player
    participant GameController
    participant StateMachine
    participant PlayBlockState
    participant Board
    participant NextBlockController
    participant MoveBlocksState
    participant MergeBlocksState
    participant FoundationsController

    Player->>GameController: Tap on column
    GameController->>StateMachine: Handle input
    StateMachine->>PlayBlockState: Process placement
    
    PlayBlockState->>Board: GetTopEmptyCell(column)
    Board-->>PlayBlockState: Return cell
    
    alt Valid placement
        PlayBlockState->>NextBlockController: GetCurrentBlock()
        NextBlockController-->>PlayBlockState: Return block
        
        PlayBlockState->>Board: PlaceBlock(block, cell)
        Board->>Cell: SetBlock(block)
        
        PlayBlockState->>StateMachine: ChangeState<MoveBlocksState>()
        StateMachine->>MoveBlocksState: Enter()
        
        MoveBlocksState->>Board: ApplyGravity()
        MoveBlocksState->>MoveBlocksState: AnimateMovement()
        
        MoveBlocksState->>StateMachine: ChangeState<MergeBlocksState>()
        StateMachine->>MergeBlocksState: Enter()
        
        MergeBlocksState->>Board: FindMergeableGroups()
        Board-->>MergeBlocksState: Return groups
        
        loop For each mergeable group
            MergeBlocksState->>MergeBlocksState: AnimateMerge()
            MergeBlocksState->>FoundationsController: TryAttachToFoundation()
            FoundationsController-->>MergeBlocksState: Success/Failure
        end
        
        MergeBlocksState->>GameController: UpdateScore()
        MergeBlocksState->>StateMachine: ChangeState<ExtractBlockState>()
        
    else Invalid placement
        PlayBlockState->>Player: Show invalid feedback
    end
```