# Use Case Diagram

```mermaid
graph LR
    Player((Player))
    
    subgraph "Merge Solitaire System"
        UC1[Start New Game]
        UC2[Place Block]
        UC3[View Next Block]
        UC4[Pause Game]
        UC5[Resume Game]
        UC6[View Score]
        UC7[View High Score]
        UC8[Restart Game]
        UC9[Quit Game]
        UC10[View Foundations]
        UC11[Merge Blocks]
        UC12[Complete Foundation]
        UC13[Win Game]
        UC14[Lose Game]
    end
    
    Player --> UC1
    Player --> UC2
    Player --> UC3
    Player --> UC4
    Player --> UC5
    Player --> UC6
    Player --> UC7
    Player --> UC8
    Player --> UC9
    Player --> UC10
    
    UC2 --> UC11 : triggers
    UC11 --> UC12 : may trigger
    UC12 --> UC13 : may trigger
    UC2 --> UC14 : may trigger
    
    UC1 -.-> UC2 : enables
    UC4 -.-> UC5 : enables
    UC13 -.-> UC8 : enables
    UC14 -.-> UC8 : enables
```