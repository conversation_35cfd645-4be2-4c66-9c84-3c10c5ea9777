# Board System - Class Diagram

```mermaid
classDiagram
    class Board {
        -Cell[,] _cells
        -int _width
        -int _height
        +Width : int
        +Height : int
        +Cells : Cell[]
        +GetCell(int, int) : Cell
        +GetColumn(int) : Cell[]
        +IsValidPosition(int, int) : bool
        +IsColumnFull(int) : bool
        +GetTopEmptyCell(int) : Cell
    }

    class Cell {
        -Block _block
        -Vector2Int _position
        -Transform _transform
        +Block : Block
        +Position : Vector2Int
        +IsEmpty : bool
        +HasBlock : bool
        +SetBlock(Block)
        +RemoveBlock()
        +GetWorldPosition() : Vector3
    }

    class GridBuilder {
        -Cell _cellPrefab
        -Board _board
        -BlockFactory _blockFactory
        +Init(Board, BlockFactory)
        +CreateGrid()
        +PopulateGrid()
        -CreateCell(int, int) : Cell
        -PlaceInitialBlocks()
    }

    class FoundationsController {
        -Foundation[] _foundations
        +Foundations : Foundation[]
        +CanAttachBlock(Block) : bool
        +AttachBlock(Block) : bool
        +IsComplete() : bool
        +GetFoundationForSeed(BlockSeed) : Foundation
    }

    class Foundation {
        -BlockSeed _seed
        -List~Block~ _blocks
        -Transform _transform
        +Seed : BlockSeed
        +Blocks : List~Block~
        +TopValue : BlockValue
        +IsComplete : bool
        +CanAttach(Block) : bool
        +AttachBlock(Block)
        +GetNextExpectedValue() : BlockValue
    }

    Board --> Cell
    Cell --> Block
    GridBuilder --> Board
    GridBuilder --> Cell
    GridBuilder --> BlockFactory
    FoundationsController --> Foundation
    Foundation --> Block
    Foundation --> BlockSeed
```