# Assembly Structure - Package Diagram

```mermaid
graph TB
    subgraph "Whatwapp.MergeSolitaire.Game"
        GameCore[Game Core]
        GameStates[Game States]
        GameUI[Game UI]
        GameSettings[Game Settings]
    end
    
    subgraph "Whatwapp.Core.UI"
        CoreUI[Core UI Components]
        Panels[Panel System]
    end
    
    subgraph "Whatwapp.Core.Utils"
        Utils[Utility Classes]
        Extensions[Extensions]
        Executables[Executables]
    end
    
    subgraph "Whatwapp.Core.Cameras"
        CameraSystem[Camera Controllers]
    end
    
    subgraph "DOTween.Modules"
        DOTweenCore[DOTween Core]
        DOTweenUI[DOTween UI]
        DOTweenModules[DOTween Modules]
    end
    
    subgraph "Unity Packages"
        UnityEngine[Unity Engine]
        UnityUI[Unity UI]
        TextMeshPro[TextMesh Pro]
    end
    
    GameCore --> CoreUI
    GameCore --> Utils
    GameCore --> CameraSystem
    GameCore --> DOTweenCore
    GameStates --> Utils
    GameStates --> DOTweenCore
    GameUI --> CoreUI
    GameUI --> DOTweenUI
    
    CoreUI --> UnityUI
    CoreUI --> TextMeshPro
    Utils --> UnityEngine
    CameraSystem --> UnityEngine
    DOTweenCore --> UnityEngine
    DOTweenUI --> UnityUI
    
    style GameCore fill:#e1f5fe
    style CoreUI fill:#e8f5e8
    style Utils fill:#fff3e0
    style DOTweenCore fill:#f3e5f5
```