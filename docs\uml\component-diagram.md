# Architecture Overview - Component Diagram

```mermaid
graph TB
    subgraph "Presentation Layer"
        UI[UI Controllers]
        Animations[Animation System]
        Audio[Audio System]
        Input[Input Handler]
    end
    
    subgraph "Game Logic Layer"
        GameController[Game Controller]
        StateMachine[State Machine]
        GameStates[Game States]
    end
    
    subgraph "Core Systems"
        BlockSystem[Block System]
        BoardSystem[Board System]
        FoundationSystem[Foundation System]
        ScoreSystem[Score System]
    end
    
    subgraph "Data Layer"
        Settings[Game Settings]
        SaveData[Save Data]
        AssetData[Asset Data]
    end
    
    subgraph "Unity Framework"
        MonoBehaviour[MonoBehaviour]
        ScriptableObject[ScriptableObject]
        DOTween[DOTween]
        PlayerPrefs[PlayerPrefs]
    end
    
    subgraph "External Dependencies"
        UnityEngine[Unity Engine]
        UnityUI[Unity UI]
        TextMeshPro[TextMesh Pro]
    end

    UI --> GameController
    Input --> GameController
    GameController --> StateMachine
    StateMachine --> GameStates
    GameStates --> BlockSystem
    GameStates --> BoardSystem
    GameStates --> FoundationSystem
    GameStates --> ScoreSystem
    
    Animations --> DOTween
    GameStates --> Animations
    Settings --> ScriptableObject
    SaveData --> PlayerPrefs
    
    BlockSystem --> MonoBehaviour
    BoardSystem --> MonoBehaviour
    UI --> UnityUI
    UI --> TextMeshPro
    
    style GameController fill:#e1f5fe
    style StateMachine fill:#e8f5e8
    style BlockSystem fill:#fff3e0
    style BoardSystem fill:#fff3e0
    style FoundationSystem fill:#fff3e0
```