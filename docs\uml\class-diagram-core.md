# Core Game Architecture - Class Diagram

```mermaid
classDiagram
    class GameController {
        -StateMachine _stateMachine
        -int _score
        -int _highScore
        -bool _isPaused
        +Score : int
        +HighScore : int
        +IsPaused : bool
        +StartGame()
        +PauseGame()
        +ResumeGame()
        +EndGame()
    }

    class StateMachine {
        -IState _currentState
        -Dictionary~Type, IState~ _states
        +ChangeState~T~()
        +Update()
        +FixedUpdate()
    }

    class IState {
        <<interface>>
        +Enter()
        +Exit()
        +Update()
        +FixedUpdate()
    }

    class GenerateLevelState {
        -GridBuilder _gridBuilder
        -Board _board
        -BlockFactory _blockFactory
        +Enter()
        +Exit()
        -GenerateLevel() : IEnumerator
    }

    class PlayBlockState {
        -NextBlockController _nextBlockController
        -Board _board
        -InputHandler _inputHandler
        +Enter()
        +Exit()
        +Update()
        -HandleInput()
    }

    class MoveBlocksState {
        -Board _board
        -AnimationSettings _animationSettings
        +Enter()
        +Exit()
        -MoveBlocks() : IEnumerator
    }

    class MergeBlocksState {
        -Board _board
        -FoundationsController _foundationsController
        -AnimationSettings _animationSettings
        +MergeCompleted : bool
        +Enter()
        +Exit()
        -GetAllMergableCells() : List~List~Cell~~
        -GetMergeableCells(Cell, bool[,]) : List~Cell~
        -AnimateMerge(List~Cell~) : Sequence
    }

    class ExtractBlockState {
        -NextBlockController _nextBlockController
        +Enter()
        +Exit()
    }

    class GameOverState {
        +Enter()
        +Exit()
    }

    class VictoryState {
        +Enter()
        +Exit()
    }

    GameController --> StateMachine
    StateMachine --> IState
    IState <|-- GenerateLevelState
    IState <|-- PlayBlockState
    IState <|-- MoveBlocksState
    IState <|-- MergeBlocksState
    IState <|-- ExtractBlockState
    IState <|-- GameOverState
    IState <|-- VictoryState
```