# Game State Machine Diagram

```mermaid
stateDiagram-v2
    [*] --> GenerateLevelState : Game Start
    
    GenerateLevelState --> ExtractBlockState : Level Generated
    
    ExtractBlockState --> PlayBlockState : Block Ready
    
    PlayBlockState --> MoveBlocksState : Block Placed
    PlayBlockState --> GameOverState : No Valid Moves
    
    MoveBlocksState --> MergeBlocksState : Movement Complete
    
    MergeBlocksState --> VictoryState : All Foundations Complete
    MergeBlocksState --> ExtractBlockState : Merge Complete
    
    VictoryState --> [*] : Game End
    GameOverState --> [*] : Game End
    
    note right of GenerateLevelState
        - Initialize board
        - Create grid
        - Place initial blocks
        - Setup camera targets
    end note
    
    note right of ExtractBlockState
        - Generate next block
        - Update UI display
        - Prepare for placement
    end note
    
    note right of PlayBlockState
        - Handle player input
        - Validate placement
        - Show preview
        - Check game over conditions
    end note
    
    note right of MoveBlocksState
        - Apply gravity
        - Animate block movement
        - Update board state
    end note
    
    note right of MergeBlocksState
        - Detect mergeable groups
        - Animate merge effects
        - Update foundations
        - Calculate score
    end note
```