<svg id="export-svg" width="100%" xmlns="http://www.w3.org/2000/svg" class="classDiagram" style="max-width: 874.58984375px;" viewBox="0 0 874.58984375 1294" role="graphics-document document" aria-roledescription="class"><style xmlns="http://www.w3.org/1999/xhtml">/* Google Inc.

This Font Software is licensed under the SIL Open Font License, Version 1.1.
This license is copied below, and is also available with a FAQ at:
http://scripts.sil.org/OFL


-----------------------------------------------------------
SIL OPEN FONT LICENSE Version 1.1 - 26 February 2007
-----------------------------------------------------------

PREAMBLE
The goals of the Open Font License (OFL) are to stimulate worldwide
development of collaborative font projects, to support the font creation
efforts of academic and linguistic communities, and to provide a free and
open framework in which fonts may be shared and improved in partnership
with others.

The OFL allows the licensed fonts to be used, studied, modified and
redistributed freely as long as they are not sold by themselves. The
fonts, including any derivative works, can be bundled, embedded,
redistributed and/or sold with any software provided that any reserved
names are not used by derivative works. The fonts and derivatives,
however, cannot be released under any other type of license. The
requirement for fonts to remain under this license does not apply
to any document created using the fonts or their derivatives.

DEFINITIONS
"Font Software" refers to the set of files released by the Copyright
Holder(s) under this license and clearly marked as such. This may
include source files, build scripts and documentation.

"Reserved Font Name" refers to any names specified as such after the
copyright statement(s).

"Original Version" refers to the collection of Font Software components as
distributed by the Copyright Holder(s).

"Modified Version" refers to any derivative made by adding to, deleting,
or substituting -- in part or in whole -- any of the components of the
Original Version, by changing formats or by porting the Font Software to a
new environment.

"Author" refers to any designer, engineer, programmer, technical
writer or other person who contributed to the Font Software.

PERMISSION &amp; CONDITIONS
Permission is hereby granted, free of charge, to any person obtaining
a copy of the Font Software, to use, study, copy, merge, embed, modify,
redistribute, and sell modified and unmodified copies of the Font
Software, subject to the following conditions:

1) Neither the Font Software nor any of its individual components,
in Original or Modified Versions, may be sold by itself.

2) Original or Modified Versions of the Font Software may be bundled,
redistributed and/or sold with any software, provided that each copy
contains the above copyright notice and this license. These can be
included either as stand-alone text files, human-readable headers or
in the appropriate machine-readable metadata fields within text or
binary files as long as those fields can be easily viewed by the user.

3) No Modified Version of the Font Software may use the Reserved Font
Name(s) unless explicit written permission is granted by the corresponding
Copyright Holder. This restriction only applies to the primary font name as
presented to the users.

4) The name(s) of the Copyright Holder(s) or the Author(s) of the Font
Software shall not be used to promote, endorse or advertise any
Modified Version, except to acknowledge the contribution(s) of the
Copyright Holder(s) and the Author(s) or with their explicit written
permission.

5) The Font Software, modified or unmodified, in part or in whole,
must be distributed entirely under this license, and must not be
distributed under any other license. The requirement for fonts to
remain under this license does not apply to any document created
using the Font Software.

TERMINATION
This license becomes null and void if any of the above conditions are
not met.

DISCLAIMER
THE FONT SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO ANY WARRANTIES OF
MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT
OF COPYRIGHT, PATENT, TRADEMARK, OR OTHER RIGHT. IN NO EVENT SHALL THE
COPYRIGHT HOLDER BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
INCLUDING ANY GENERAL, SPECIAL, INDIRECT, INCIDENTAL, OR CONSEQUENTIAL
DAMAGES, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
FROM, OUT OF THE USE OR INABILITY TO USE THE FONT SOFTWARE OR FROM
OTHER DEALINGS IN THE FONT SOFTWARE.
 */
@font-face {
  font-family: 'Recursive Variable';
  font-style: normal;
  font-display: swap;
  font-weight: 300 1000;
  src: url("data:font/woff2;base64,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") format('woff2-variations');
  unicode-range: U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD;
}</style><style xmlns="http://www.w3.org/1999/xhtml">@import url("https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/css/all.min.css"); p {margin: 0;}</style><style>#export-svg{font-family:"Recursive Variable",arial,sans-serif;font-size:14px;fill:#28253D;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#export-svg .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#export-svg .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#export-svg .error-icon{fill:#ffffff;}#export-svg .error-text{fill:#000000;stroke:#000000;}#export-svg .edge-thickness-normal{stroke-width:2px;}#export-svg .edge-thickness-thick{stroke-width:3.5px;}#export-svg .edge-pattern-solid{stroke-dasharray:0;}#export-svg .edge-thickness-invisible{stroke-width:0;fill:none;}#export-svg .edge-pattern-dashed{stroke-dasharray:3;}#export-svg .edge-pattern-dotted{stroke-dasharray:2;}#export-svg .marker{fill:#000000;stroke:#000000;}#export-svg .marker.cross{stroke:#000000;}#export-svg svg{font-family:"Recursive Variable",arial,sans-serif;font-size:14px;}#export-svg p{margin:0;}#export-svg g.classGroup text{fill:#28253D;stroke:none;font-family:"Recursive Variable",arial,sans-serif;font-size:10px;}#export-svg g.classGroup text .title{font-weight:bolder;}#export-svg .nodeLabel,#export-svg .edgeLabel{color:#28253D;}#export-svg .noteLabel .nodeLabel,#export-svg .noteLabel .edgeLabel{color:#28253D;}#export-svg .edgeLabel .label rect{fill:#ffffff;}#export-svg .label text{fill:#28253D;}#export-svg .labelBkg{background:#ffffff;}#export-svg .edgeLabel .label span{background:#ffffff;}#export-svg .classTitle{font-weight:bolder;}#export-svg .node rect,#export-svg .node circle,#export-svg .node ellipse,#export-svg .node polygon,#export-svg .node path{fill:#ffffff;stroke:#28253D;stroke-width:2;}#export-svg .divider{stroke:#28253D;stroke-width:1;}#export-svg g.clickable{cursor:pointer;}#export-svg g.classGroup rect{fill:#ffffff;stroke:#28253D;}#export-svg g.classGroup line{stroke:#28253D;stroke-width:1;}#export-svg .classLabel .box{stroke:none;stroke-width:0;fill:#ffffff;opacity:0.5;}#export-svg .classLabel .label{fill:#28253D;font-size:10px;}#export-svg .relation{stroke:#000000;stroke-width:2;fill:none;}#export-svg .dashed-line{stroke-dasharray:3;}#export-svg .dotted-line{stroke-dasharray:1 2;}#export-svg #compositionStart,#export-svg .composition{fill:#000000!important;stroke:#000000!important;stroke-width:1;}#export-svg #compositionEnd,#export-svg .composition{fill:#000000!important;stroke:#000000!important;stroke-width:1;}#export-svg #dependencyStart,#export-svg .dependency{fill:#000000!important;stroke:#000000!important;stroke-width:1;}#export-svg #dependencyStart,#export-svg .dependency{fill:#000000!important;stroke:#000000!important;stroke-width:1;}#export-svg #extensionStart,#export-svg .extension{fill:transparent!important;stroke:#000000!important;stroke-width:1;}#export-svg #extensionEnd,#export-svg .extension{fill:transparent!important;stroke:#000000!important;stroke-width:1;}#export-svg #aggregationStart,#export-svg .aggregation{fill:transparent!important;stroke:#000000!important;stroke-width:1;}#export-svg #aggregationEnd,#export-svg .aggregation{fill:transparent!important;stroke:#000000!important;stroke-width:1;}#export-svg #lollipopStart,#export-svg .lollipop{fill:#ffffff!important;stroke:#000000!important;stroke-width:1;}#export-svg #lollipopEnd,#export-svg .lollipop{fill:#ffffff!important;stroke:#000000!important;stroke-width:1;}#export-svg .edgeTerminals{font-size:11px;line-height:initial;}#export-svg .classTitleText{text-anchor:middle;font-size:18px;fill:#28253D;}#export-svg .edgeLabel{background-color:hsl(-120, 0%, 80%);text-align:center;}#export-svg .edgeLabel p{background-color:hsl(-120, 0%, 80%);}#export-svg .edgeLabel rect{opacity:0.5;background-color:hsl(-120, 0%, 80%);fill:hsl(-120, 0%, 80%);}#export-svg .cluster rect{fill:#F9F9FB;stroke:#BDBCCC;stroke-width:2px;}#export-svg .label-icon{display:inline-block;height:1em;overflow:visible;vertical-align:-0.125em;}#export-svg .node .label-icon path{fill:currentColor;stroke:revert;stroke-width:revert;}#export-svg .node .neo-node{stroke:#28253D;}#export-svg [data-look="neo"].node rect,#export-svg [data-look="neo"].cluster rect,#export-svg [data-look="neo"].node polygon{stroke:#28253D;filter:url(#drop-shadow);}#export-svg [data-look="neo"].node path{stroke:#28253D;stroke-width:2;}#export-svg [data-look="neo"].node .outer-path{filter:url(#drop-shadow);}#export-svg [data-look="neo"].node .neo-line path{stroke:#28253D;filter:none;}#export-svg [data-look="neo"].node circle{stroke:#28253D;filter:url(#drop-shadow);}#export-svg [data-look="neo"].node circle .state-start{fill:#000000;}#export-svg [data-look="neo"].statediagram-cluster rect{fill:#F9F9FB;stroke:#28253D;stroke-width:2;}#export-svg [data-look="neo"].icon-shape .icon{fill:#28253D;filter:url(#drop-shadow);}#export-svg [data-look="neo"].icon-shape .icon-neo path{stroke:#28253D;filter:url(#drop-shadow);}#export-svg :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><defs><marker id="export-svg_class-aggregationStart" class="marker aggregation class" refX="18" refY="7" markerWidth="190" markerHeight="240" orient="auto"><path d="M 18,7 L9,13 L1,7 L9,1 Z"/></marker></defs><defs><marker id="export-svg_class-aggregationEnd" class="marker aggregation class" refX="1" refY="7" markerWidth="20" markerHeight="28" orient="auto"><path d="M 18,7 L9,13 L1,7 L9,1 Z"/></marker></defs><defs><marker id="export-svg_class-aggregationStart-margin" class="marker aggregation class" refX="15" refY="7" markerWidth="190" markerHeight="240" orient="auto" markerUnits="userSpaceOnUse"><path d="M 18,7 L9,13 L1,7 L9,1 Z" style="stroke-width: 2;"/></marker></defs><defs><marker id="export-svg_class-aggregationEnd-margin" class="marker aggregation class" refX="1" refY="7" markerWidth="20" markerHeight="28" orient="auto" markerUnits="userSpaceOnUse"><path d="M 18,7 L9,13 L1,7 L9,1 Z" style="stroke-width: 2;"/></marker></defs><defs><marker id="export-svg_class-extensionStart" class="marker extension class" refX="17.5" refY="7" markerWidth="190" markerHeight="240" orient="auto" markerUnits="userSpaceOnUse"><path d="M 1,7 L18,13 V 1 Z"/></marker></defs><defs><marker id="export-svg_class-extensionEnd" class="marker extension class" refX="1" refY="7" markerWidth="20" markerHeight="28" orient="auto"><path d="M 1,1 V 13 L18,7 Z"/></marker></defs><marker id="export-svg_class-extensionStart-margin" class="marker extension class" refX="18" refY="7" markerWidth="20" markerHeight="28" orient="auto" markerUnits="userSpaceOnUse" viewBox="0 0 20 14"><polygon points="10,7 18,13 18,1" style="stroke-width: 2; stroke-dasharray: 0;"/></marker><defs><marker id="export-svg_class-extensionEnd-margin" class="marker extension class" refX="9" refY="7" markerWidth="20" markerHeight="28" orient="auto" markerUnits="userSpaceOnUse" viewBox="0 0 20 14"><polygon points="10,1 10,13 18,7" style="stroke-width: 2; stroke-dasharray: 0;"/></marker></defs><defs><marker id="export-svg_class-compositionStart" class="marker composition class" refX="18" refY="7" markerWidth="190" markerHeight="240" orient="auto"><path d="M 18,7 L9,13 L1,7 L9,1 Z"/></marker></defs><defs><marker id="export-svg_class-compositionEnd" class="marker composition class" refX="1" refY="7" markerWidth="20" markerHeight="28" orient="auto"><path d="M 18,7 L9,13 L1,7 L9,1 Z"/></marker></defs><defs><marker id="export-svg_class-compositionStart-margin" class="marker composition class" refX="15" refY="7" markerWidth="190" markerHeight="240" orient="auto" markerUnits="userSpaceOnUse"><path viewBox="0 0 15 15" d="M 18,7 L9,13 L1,7 L9,1 Z" style="stroke-width: 0;"/></marker></defs><defs><marker id="export-svg_class-compositionEnd-margin" class="marker composition class" refX="3.5" refY="7" markerWidth="20" markerHeight="28" orient="auto" markerUnits="userSpaceOnUse"><path d="M 18,7 L9,13 L1,7 L9,1 Z" style="stroke-width: 0;"/></marker></defs><defs><marker id="export-svg_class-dependencyStart" class="marker dependency class" refX="6" refY="7" markerWidth="190" markerHeight="240" orient="auto"><path d="M 5,7 L9,13 L1,7 L9,1 Z"/></marker></defs><defs><marker id="export-svg_class-dependencyEnd" class="marker dependency class" refX="13" refY="7" markerWidth="20" markerHeight="28" orient="auto"><path d="M 18,7 L9,13 L14,7 L9,1 Z"/></marker></defs><defs><marker id="export-svg_class-dependencyStart-margin" class="marker dependency class" refX="4" refY="7" markerWidth="190" markerHeight="240" orient="auto" markerUnits="userSpaceOnUse"><path d="M 5,7 L9,13 L1,7 L9,1 Z" style="stroke-width: 0;"/></marker></defs><defs><marker id="export-svg_class-dependencyEnd-margin" class="marker dependency class" refX="16" refY="7" markerWidth="20" markerHeight="28" orient="auto" markerUnits="userSpaceOnUse"><path d="M 18,7 L9,13 L14,7 L9,1 Z" style="stroke-width: 0;"/></marker></defs><defs><marker id="export-svg_class-lollipopStart" class="marker lollipop class" refX="13" refY="7" markerWidth="190" markerHeight="240" orient="auto"><circle fill="transparent" cx="7" cy="7" r="6"/></marker></defs><defs><marker id="export-svg_class-lollipopEnd" class="marker lollipop class" refX="1" refY="7" markerWidth="190" markerHeight="240" orient="auto"><circle fill="transparent" cx="7" cy="7" r="6"/></marker></defs><defs><marker id="export-svg_class-lollipopStart-margin" class="marker lollipop class" refX="13" refY="7" markerWidth="190" markerHeight="240" orient="auto" markerUnits="userSpaceOnUse"><circle fill="transparent" cx="7" cy="7" r="6" stroke-width="2"/></marker></defs><defs><marker id="export-svg_class-lollipopEnd-margin" class="marker lollipop class" refX="1" refY="7" markerWidth="190" markerHeight="240" orient="auto" markerUnits="userSpaceOnUse"><circle fill="transparent" cx="7" cy="7" r="6" stroke-width="2"/></marker></defs><g class="root"><g class="clusters"/><g class="edgePaths"><path d="M318.6484375,816.3977072310406L261.1783954021437,867.675252141398Q254.08984375,874 254.08984375,883.5L254.08984375,893" id="id_Block_BlockValue_1" class="edge-thickness-normal edge-pattern-solid relation" style="stroke-dasharray: 0 0 104.42425537109375 0; stroke-dashoffset: 0;;;;" data-edge="true" data-et="edge" data-id="id_Block_BlockValue_1" data-points="W3sieCI6MzE4LjY0ODQzNzUsInkiOjgxNi4zOTc3MDcyMzEwNDA2fSx7IngiOjI1NC4wODk4NDM3NSwieSI6ODc0fSx7IngiOjI1NC4wODk4NDM3NSwieSI6ODk5fV0=" marker-end="url(#export-svg_class-dependencyEnd-margin)"/><path d="M440.13671875,849L440.13671875,874L440.13671875,987.5" id="id_Block_BlockSeed_2" class="edge-thickness-normal edge-pattern-solid relation" style="stroke-dasharray: 0 0 138.5 0; stroke-dashoffset: 0;;;;" data-edge="true" data-et="edge" data-id="id_Block_BlockSeed_2" data-points="W3sieCI6NDQwLjEzNjcxODc1LCJ5Ijo4NDl9LHsieCI6NDQwLjEzNjcxODc1LCJ5Ijo4NzR9LHsieCI6NDQwLjEzNjcxODc1LCJ5Ijo5OTMuNX1d" marker-end="url(#export-svg_class-dependencyEnd-margin)"/><path d="M561.625,781.5751175716118L705.5261549767638,868.7238062119503Q714.23828125,874 714.23828125,884.1852523331464L714.23828125,977" id="id_Block_BlockVisual_3" class="edge-thickness-normal edge-pattern-solid relation" style="stroke-dasharray: 0 0 279.69390869140625 0; stroke-dashoffset: 0;;;;" data-edge="true" data-et="edge" data-id="id_Block_BlockVisual_3" data-points="W3sieCI6NTYxLjYyNSwieSI6NzgxLjU3NTExNzU3MTYxMTh9LHsieCI6NzE0LjIzODI4MTI1LCJ5Ijo4NzR9LHsieCI6NzE0LjIzODI4MTI1LCJ5Ijo5ODN9XQ==" marker-end="url(#export-svg_class-dependencyEnd-margin)"/><path d="M182.125,517L182.125,531.5621364582655Q182.125,542 190.9030113211103,547.6476112262026L313.6025708131943,626.5902537323811" id="id_BlockFactory_Block_4" class="edge-thickness-normal edge-pattern-solid relation" style="stroke-dasharray: 0 0 179.66067504882812 0; stroke-dashoffset: 0;;;;" data-edge="true" data-et="edge" data-id="id_BlockFactory_Block_4" data-points="W3sieCI6MTgyLjEyNSwieSI6NTE3fSx7IngiOjE4Mi4xMjUsInkiOjU0Mn0seyJ4IjozMTguNjQ4NDM3NSwieSI6NjI5LjgzNjY3MTY2MjgwNn1d" marker-end="url(#export-svg_class-dependencyEnd-margin)"/><path d="M362.5,230.07532766815496L190.56884631540186,311.02444417709796Q182.125,315 182.125,324.3329301239823L182.125,334" id="id_NextBlockController_BlockFactory_5" class="edge-thickness-normal edge-pattern-solid relation" style="stroke-dasharray: 0 0 216.46405029296875 0; stroke-dashoffset: 0;;;;" data-edge="true" data-et="edge" data-id="id_NextBlockController_BlockFactory_5" data-points="W3sieCI6MzYyLjUsInkiOjIzMC4wNzUzMjc2NjgxNTQ5Nn0seyJ4IjoxODIuMTI1LCJ5IjozMTV9LHsieCI6MTgyLjEyNSwieSI6MzQwfV0=" marker-end="url(#export-svg_class-dependencyEnd-margin)"/><path d="M706.8984375,262.6565659703347L777.3972764773197,309.1878801146257Q786.203125,315 786.203125,325.5510049647583L786.203125,428.5L786.203125,532.6135832401782Q786.203125,542 777.7399868231314,546.0595703945482L567.0348204203514,647.1299343223138" id="id_NextBlockController_Block_6" class="edge-thickness-normal edge-pattern-solid relation" style="stroke-dasharray: 0 0 561.5504150390625 0; stroke-dashoffset: 0;;;;" data-edge="true" data-et="edge" data-id="id_NextBlockController_Block_6" data-points="W3sieCI6NzA2Ljg5ODQzNzUsInkiOjI2Mi42NTY1NjU5NzAzMzQ3fSx7IngiOjc4Ni4yMDMxMjUsInkiOjMxNX0seyJ4Ijo3ODYuMjAzMTI1LCJ5Ijo0MjguNX0seyJ4Ijo3ODYuMjAzMTI1LCJ5Ijo1NDJ9LHsieCI6NTYxLjYyNSwieSI6NjQ5LjcyNDg5OTI1ODQwNjR9XQ==" marker-end="url(#export-svg_class-dependencyEnd-margin)"/><path d="M572.095938441265,290L576.2911291134782,305.8174805080623Q578.7265625,315 578.7265625,324.5L578.7265625,334" id="id_NextBlockController_BlockGenerationSettings_7" class="edge-thickness-normal edge-pattern-solid relation" style="stroke-dasharray: 0 0 44.75821304321289 0; stroke-dashoffset: 0;;;;" data-edge="true" data-et="edge" data-id="id_NextBlockController_BlockGenerationSettings_7" data-points="W3sieCI6NTcyLjA5NTkzODQ0MTI2NSwieSI6MjkwfSx7IngiOjU3OC43MjY1NjI1LCJ5IjozMTV9LHsieCI6NTc4LjcyNjU2MjUsInkiOjM0MH1d" marker-end="url(#export-svg_class-dependencyEnd-margin)"/></g><g class="edgeLabels"><g class="edgeLabel"><g class="label" data-id="id_Block_BlockValue_1" transform="translate(0, -10.5)"><foreignObject width="0" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml" class="labelBkg"><span style=";display: inline-block" class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" data-id="id_Block_BlockSeed_2" transform="translate(0, -10.5)"><foreignObject width="0" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml" class="labelBkg"><span style=";display: inline-block" class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" data-id="id_Block_BlockVisual_3" transform="translate(0, -10.5)"><foreignObject width="0" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml" class="labelBkg"><span style=";display: inline-block" class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" data-id="id_BlockFactory_Block_4" transform="translate(0, -10.5)"><foreignObject width="0" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml" class="labelBkg"><span style=";display: inline-block" class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" data-id="id_NextBlockController_BlockFactory_5" transform="translate(0, -10.5)"><foreignObject width="0" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml" class="labelBkg"><span style=";display: inline-block" class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" data-id="id_NextBlockController_Block_6" transform="translate(0, -10.5)"><foreignObject width="0" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml" class="labelBkg"><span style=";display: inline-block" class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" data-id="id_NextBlockController_BlockGenerationSettings_7" transform="translate(0, -10.5)"><foreignObject width="0" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml" class="labelBkg"><span style=";display: inline-block" class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g class="node default" id="classId-Block-35" data-id="Block" data-node="true" data-et="node" data-look="neo" transform="translate(440.13671875, 708)"><g class="basic label-container outer-path"><path d="M-121.48828125 -141 L121.48828125 -141 L121.48828125 141 L-121.48828125 141" stroke="none" stroke-width="0" fill="#ffffff" style=""/><path d="M-121.48828125 -141 C-47.541017814174594 -141, 26.40624562165081 -141, 121.48828125 -141 M-121.48828125 -141 C-27.796299080929145 -141, 65.89568308814171 -141, 121.48828125 -141 M121.48828125 -141 C121.48828125 -38.58774644866297, 121.48828125 63.82450710267406, 121.48828125 141 M121.48828125 -141 C121.48828125 -63.87456805695325, 121.48828125 13.250863886093498, 121.48828125 141 M121.48828125 141 C38.98486762948896 141, -43.51854599102208 141, -121.48828125 141 M121.48828125 141 C43.02898306783848 141, -35.43031511432304 141, -121.48828125 141 M-121.48828125 141 C-121.48828125 49.061627989680105, -121.48828125 -42.87674402063979, -121.48828125 -141 M-121.48828125 141 C-121.48828125 52.876596820161225, -121.48828125 -35.24680635967755, -121.48828125 -141" stroke="#28253D" stroke-width="1.3" fill="none" style=""/></g><g class="annotation-group text" transform="translate(0, -117)"/><g class="label-group text" transform="translate(-18.5546875, -117)"><g class="label" style="font-weight: bolder" transform="translate(0,-10.5)"><foreignObject width="37.109375" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 93px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel markdown-node-label" style=""><p>Block</p></span></div></foreignObject></g></g><g class="members-group text" transform="translate(-109.48828125, -72)"><g class="label" style="" transform="translate(0,-10.5)"><foreignObject width="131.1875" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 210px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel markdown-node-label" style=""><p>-BlockValue _value</p></span></div></foreignObject></g><g class="label" style="" transform="translate(0,10.5)"><foreignObject width="121.8125" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 199px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel markdown-node-label" style=""><p>-BlockSeed _seed</p></span></div></foreignObject></g><g class="label" style="" transform="translate(0,31.5)"><foreignObject width="140.5625" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 221px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel markdown-node-label" style=""><p>-BlockVisual _visual</p></span></div></foreignObject></g><g class="label" style="" transform="translate(0,52.5)"><foreignObject width="135.953125" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 215px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel markdown-node-label" style=""><p>+Value : BlockValue</p></span></div></foreignObject></g><g class="label" style="" transform="translate(0,73.5)"><foreignObject width="125.59375" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 203px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel markdown-node-label" style=""><p>+Seed : BlockSeed</p></span></div></foreignObject></g><g class="label" style="" transform="translate(0,94.5)"><foreignObject width="145.46875" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 226px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel markdown-node-label" style=""><p>+Visual : BlockVisual</p></span></div></foreignObject></g></g><g class="methods-group text" transform="translate(-109.48828125, 78)"><g class="label" style="" transform="translate(0,-10.5)"><foreignObject width="200.421875" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 289px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel markdown-node-label" style=""><p>+Init(BlockValue, BlockSeed)</p></span></div></foreignObject></g><g class="label" style="" transform="translate(0,10.5)"><foreignObject width="86.25" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 158px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel markdown-node-label" style=""><p>+SetVisual()</p></span></div></foreignObject></g><g class="label" style="" transform="translate(0,31.5)"><foreignObject width="74.34375" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 145px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel markdown-node-label" style=""><p>+Destroy()</p></span></div></foreignObject></g></g><g class="divider neo-line" style=""><path d="M-121.48828125 -96 C-52.79872149331189 -95.99971729964795, 15.890838263376224 -95.99943459929591, 121.48828125 -95.999 M-121.48828125 -96 C-40.3172918695645 -95.99966593078548, 40.853697510871 -95.99933186157097, 121.48828125 -95.999" stroke="#28253D" stroke-width="1.3" fill="none" style=""/></g><g class="divider neo-line" style=""><path d="M-121.48828125 54 C-32.25132354597149 54.00036726570162, 56.985634158057024 54.00073453140324, 121.48828125 54.001 M-121.48828125 54 C-49.19385950180035 54.00029753660602, 23.100562246399306 54.00059507321203, 121.48828125 54.001" stroke="#28253D" stroke-width="1.3" fill="none" style=""/></g></g><g class="node default" id="classId-BlockValue-36" data-id="BlockValue" data-node="true" data-et="node" data-look="neo" transform="translate(254.08984375, 1092.5)"><g class="basic label-container outer-path"><path d="M-64.296875 -193.5 L64.296875 -193.5 L64.296875 193.5 L-64.296875 193.5" stroke="none" stroke-width="0" fill="#ffffff" style=""/><path d="M-64.296875 -193.5 C-38.18329115893452 -193.5, -12.069707317869039 -193.5, 64.296875 -193.5 M-64.296875 -193.5 C-30.611526724997567 -193.5, 3.0738215500048653 -193.5, 64.296875 -193.5 M64.296875 -193.5 C64.296875 -59.19012792001246, 64.296875 75.11974415997508, 64.296875 193.5 M64.296875 -193.5 C64.296875 -82.34713625600338, 64.296875 28.805727487993238, 64.296875 193.5 M64.296875 193.5 C37.975283342897114 193.5, 11.653691685794222 193.5, -64.296875 193.5 M64.296875 193.5 C18.919958113956604 193.5, -26.456958772086793 193.5, -64.296875 193.5 M-64.296875 193.5 C-64.296875 65.52803852723304, -64.296875 -62.44392294553393, -64.296875 -193.5 M-64.296875 193.5 C-64.296875 75.54469929714185, -64.296875 -42.41060140571631, -64.296875 -193.5" stroke="#28253D" stroke-width="1.3" fill="none" style=""/></g><g class="annotation-group text" transform="translate(-52.296875, -169.5)"><g class="label" style="" transform="translate(0,-10.5)"><foreignObject width="104.59375" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 170px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel markdown-node-label" style=""><p>«enumeration»</p></span></div></foreignObject></g></g><g class="label-group text" transform="translate(-37.875, -148.5)"><g class="label" style="font-weight: bolder" transform="translate(0,-10.5)"><foreignObject width="75.75" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 137px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel markdown-node-label" style=""><p>BlockValue</p></span></div></foreignObject></g></g><g class="members-group text" transform="translate(-52.296875, -103.5)"><g class="label" style="" transform="translate(0,-10.5)"><foreignObject width="24.640625" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 78px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel markdown-node-label" style=""><p>Ace</p></span></div></foreignObject></g><g class="label" style="" transform="translate(0,10.5)"><foreignObject width="27.734375" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 82px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel markdown-node-label" style=""><p>Two</p></span></div></foreignObject></g><g class="label" style="" transform="translate(0,31.5)"><foreignObject width="39.625" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 95px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel markdown-node-label" style=""><p>Three</p></span></div></foreignObject></g><g class="label" style="" transform="translate(0,52.5)"><foreignObject width="30.390625" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 85px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel markdown-node-label" style=""><p>Four</p></span></div></foreignObject></g><g class="label" style="" transform="translate(0,73.5)"><foreignObject width="28.703125" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 83px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel markdown-node-label" style=""><p>Five</p></span></div></foreignObject></g><g class="label" style="" transform="translate(0,94.5)"><foreignObject width="21.703125" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 75px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel markdown-node-label" style=""><p>Six</p></span></div></foreignObject></g><g class="label" style="" transform="translate(0,115.5)"><foreignObject width="41.3125" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 97px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel markdown-node-label" style=""><p>Seven</p></span></div></foreignObject></g><g class="label" style="" transform="translate(0,136.5)"><foreignObject width="35.84375" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 91px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel markdown-node-label" style=""><p>Eight</p></span></div></foreignObject></g><g class="label" style="" transform="translate(0,157.5)"><foreignObject width="30.8125" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 85px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel markdown-node-label" style=""><p>Nine</p></span></div></foreignObject></g><g class="label" style="" transform="translate(0,178.5)"><foreignObject width="24.09375" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 78px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel markdown-node-label" style=""><p>Ten</p></span></div></foreignObject></g><g class="label" style="" transform="translate(0,199.5)"><foreignObject width="30.8125" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 86px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel markdown-node-label" style=""><p>Jack</p></span></div></foreignObject></g><g class="label" style="" transform="translate(0,220.5)"><foreignObject width="42.84375" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 99px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel markdown-node-label" style=""><p>Queen</p></span></div></foreignObject></g><g class="label" style="" transform="translate(0,241.5)"><foreignObject width="30.109375" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 85px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel markdown-node-label" style=""><p>King</p></span></div></foreignObject></g></g><g class="methods-group text" transform="translate(-52.296875, 193.5)"/><g class="divider neo-line" style=""><path d="M-64.296875 -127.5 C-28.869643140011604 -127.4997245026927, 6.557588719976792 -127.4994490053854, 64.296875 -127.499 M-64.296875 -127.5 C-17.051518788474908 -127.4996325999031, 30.193837423050184 -127.49926519980619, 64.296875 -127.499" stroke="#28253D" stroke-width="1.3" fill="none" style=""/></g><g class="divider neo-line" style=""><path d="M-64.296875 169.5 C-17.46127023288043 169.5003642136944, 29.37433453423914 169.50072842738885, 64.296875 169.501 M-64.296875 169.5 C-25.03793359498171 169.50030529431956, 14.221007810036582 169.50061058863912, 64.296875 169.501" stroke="#28253D" stroke-width="1.3" fill="none" style=""/></g></g><g class="node default" id="classId-BlockSeed-37" data-id="BlockSeed" data-node="true" data-et="node" data-look="neo" transform="translate(440.13671875, 1092.5)"><g class="basic label-container outer-path"><path d="M-71.75 -99 L71.75 -99 L71.75 99 L-71.75 99" stroke="none" stroke-width="0" fill="#ffffff" style=""/><path d="M-71.75 -99 C-41.23345321061137 -99, -10.716906421222752 -99, 71.75 -99 M-71.75 -99 C-16.0143187486798 -99, 39.7213625026404 -99, 71.75 -99 M71.75 -99 C71.75 -42.30837938616077, 71.75 14.383241227678454, 71.75 99 M71.75 -99 C71.75 -39.71415620323128, 71.75 19.571687593537433, 71.75 99 M71.75 99 C16.190881313526233 99, -39.36823737294753 99, -71.75 99 M71.75 99 C18.924709884730817 99, -33.90058023053837 99, -71.75 99 M-71.75 99 C-71.75 22.76965739562901, -71.75 -53.46068520874198, -71.75 -99 M-71.75 99 C-71.75 47.91040502932833, -71.75 -3.1791899413433384, -71.75 -99" stroke="#28253D" stroke-width="1.3" fill="none" style=""/></g><g class="annotation-group text" transform="translate(-52.296875, -75)"><g class="label" style="" transform="translate(0,-10.5)"><foreignObject width="104.59375" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 170px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel markdown-node-label" style=""><p>«enumeration»</p></span></div></foreignObject></g></g><g class="label-group text" transform="translate(-35.28125, -54)"><g class="label" style="font-weight: bolder" transform="translate(0,-10.5)"><foreignObject width="70.5625" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 131px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel markdown-node-label" style=""><p>BlockSeed</p></span></div></foreignObject></g></g><g class="members-group text" transform="translate(-59.75, -9)"><g class="label" style="" transform="translate(0,-10.5)"><foreignObject width="47.1875" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 104px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel markdown-node-label" style=""><p>Hearts</p></span></div></foreignObject></g><g class="label" style="" transform="translate(0,10.5)"><foreignObject width="67.203125" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 127px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel markdown-node-label" style=""><p>Diamonds</p></span></div></foreignObject></g><g class="label" style="" transform="translate(0,31.5)"><foreignObject width="37.671875" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 93px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel markdown-node-label" style=""><p>Clubs</p></span></div></foreignObject></g><g class="label" style="" transform="translate(0,52.5)"><foreignObject width="49.5625" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 107px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel markdown-node-label" style=""><p>Spades</p></span></div></foreignObject></g></g><g class="methods-group text" transform="translate(-59.75, 99)"/><g class="divider neo-line" style=""><path d="M-71.75 -33 C-31.176485566481198 -32.99971725773914, 9.397028867037605 -32.99943451547828, 71.75 -32.999 M-71.75 -33 C-41.10000558510508 -32.999786411188744, -10.450011170210168 -32.999572822377495, 71.75 -32.999" stroke="#28253D" stroke-width="1.3" fill="none" style=""/></g><g class="divider neo-line" style=""><path d="M-71.75 75 C-41.4194268011696 75.00021136287944, -11.0888536023392 75.00042272575887, 71.75 75.001 M-71.75 75 C-26.173681816490863 75.00031760500477, 19.402636367018275 75.00063521000953, 71.75 75.001" stroke="#28253D" stroke-width="1.3" fill="none" style=""/></g></g><g class="node default" id="classId-BlockVisual-38" data-id="BlockVisual" data-node="true" data-et="node" data-look="neo" transform="translate(714.23828125, 1092.5)"><g class="basic label-container outer-path"><path d="M-152.3515625 -109.5 L152.3515625 -109.5 L152.3515625 109.5 L-152.3515625 109.5" stroke="none" stroke-width="0" fill="#ffffff" style=""/><path d="M-152.3515625 -109.5 C-67.88225665271861 -109.5, 16.58704919456278 -109.5, 152.3515625 -109.5 M-152.3515625 -109.5 C-69.61360965067058 -109.5, 13.124343198658835 -109.5, 152.3515625 -109.5 M152.3515625 -109.5 C152.3515625 -36.183529800744594, 152.3515625 37.13294039851081, 152.3515625 109.5 M152.3515625 -109.5 C152.3515625 -32.41059109608834, 152.3515625 44.67881780782332, 152.3515625 109.5 M152.3515625 109.5 C83.79674621036091 109.5, 15.241929920721816 109.5, -152.3515625 109.5 M152.3515625 109.5 C55.04417731146489 109.5, -42.263207877070215 109.5, -152.3515625 109.5 M-152.3515625 109.5 C-152.3515625 50.92244323624826, -152.3515625 -7.655113527503474, -152.3515625 -109.5 M-152.3515625 109.5 C-152.3515625 55.03344371401601, -152.3515625 0.5668874280320182, -152.3515625 -109.5" stroke="#28253D" stroke-width="1.3" fill="none" style=""/></g><g class="annotation-group text" transform="translate(0, -85.5)"/><g class="label-group text" transform="translate(-40.25, -85.5)"><g class="label" style="font-weight: bolder" transform="translate(0,-10.5)"><foreignObject width="80.5" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 142px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel markdown-node-label" style=""><p>BlockVisual</p></span></div></foreignObject></g></g><g class="members-group text" transform="translate(-140.3515625, -40.5)"><g class="label" style="" transform="translate(0,-10.5)"><foreignObject width="102.90625" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 177px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel markdown-node-label" style=""><p>-Image _image</p></span></div></foreignObject></g><g class="label" style="" transform="translate(0,10.5)"><foreignObject width="124.609375" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 202px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel markdown-node-label" style=""><p>-Sprite[] _sprites</p></span></div></foreignObject></g></g><g class="methods-group text" transform="translate(-140.3515625, 25.5)"><g class="label" style="" transform="translate(0,-10.5)"><foreignObject width="240.453125" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 334px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel markdown-node-label" style=""><p>+SetSprite(BlockValue, BlockSeed)</p></span></div></foreignObject></g><g class="label" style="" transform="translate(0,10.5)"><foreignObject width="164.375" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 247px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel markdown-node-label" style=""><p>+PlaySpawnAnimation()</p></span></div></foreignObject></g><g class="label" style="" transform="translate(0,31.5)"><foreignObject width="162.265625" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 245px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel markdown-node-label" style=""><p>+PlayMergeAnimation()</p></span></div></foreignObject></g><g class="label" style="" transform="translate(0,52.5)"><foreignObject width="174.734375" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 259px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel markdown-node-label" style=""><p>+PlayDestroyAnimation()</p></span></div></foreignObject></g></g><g class="divider neo-line" style=""><path d="M-152.3515625 -64.5 C-59.68575015498352 -64.4996958816476, 32.98006219003295 -64.49939176329521, 152.3515625 -64.499 M-152.3515625 -64.5 C-50.82346662915076 -64.49966679667013, 50.704629241698484 -64.49933359334027, 152.3515625 -64.499" stroke="#28253D" stroke-width="1.3" fill="none" style=""/></g><g class="divider neo-line" style=""><path d="M-152.3515625 1.5 C-74.86588047095901 1.5002542989410728, 2.6198015580819742 1.5005085978821453, 152.3515625 1.501 M-152.3515625 1.5 C-55.74381624821331 1.5003170553181946, 40.86393000357339 1.5006341106363892, 152.3515625 1.501" stroke="#28253D" stroke-width="1.3" fill="none" style=""/></g></g><g class="node default" id="classId-BlockFactory-39" data-id="BlockFactory" data-node="true" data-et="node" data-look="neo" transform="translate(182.125, 428.5)"><g class="basic label-container outer-path"><path d="M-174.125 -88.5 L174.125 -88.5 L174.125 88.5 L-174.125 88.5" stroke="none" stroke-width="0" fill="#ffffff" style=""/><path d="M-174.125 -88.5 C-45.080435786571684 -88.5, 83.96412842685663 -88.5, 174.125 -88.5 M-174.125 -88.5 C-60.5642870329893 -88.5, 52.9964259340214 -88.5, 174.125 -88.5 M174.125 -88.5 C174.125 -35.14370591335797, 174.125 18.21258817328406, 174.125 88.5 M174.125 -88.5 C174.125 -33.00397789420943, 174.125 22.492044211581145, 174.125 88.5 M174.125 88.5 C72.7809363474621 88.5, -28.563127305075795 88.5, -174.125 88.5 M174.125 88.5 C49.13004941495811 88.5, -75.86490117008378 88.5, -174.125 88.5 M-174.125 88.5 C-174.125 29.308227418324215, -174.125 -29.88354516335157, -174.125 -88.5 M-174.125 88.5 C-174.125 48.36915809260718, -174.125 8.238316185214359, -174.125 -88.5" stroke="#28253D" stroke-width="1.3" fill="none" style=""/></g><g class="annotation-group text" transform="translate(0, -64.5)"/><g class="label-group text" transform="translate(-44.875, -64.5)"><g class="label" style="font-weight: bolder" transform="translate(0,-10.5)"><foreignObject width="89.75" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 153px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel markdown-node-label" style=""><p>BlockFactory</p></span></div></foreignObject></g></g><g class="members-group text" transform="translate(-162.125, -19.5)"><g class="label" style="" transform="translate(0,-10.5)"><foreignObject width="137.765625" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 217px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel markdown-node-label" style=""><p>-Block _blockPrefab</p></span></div></foreignObject></g></g><g class="methods-group text" transform="translate(-162.125, 25.5)"><g class="label" style="" transform="translate(0,-10.5)"><foreignObject width="279.375" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 379px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel markdown-node-label" style=""><p>+Create(BlockValue, BlockSeed) : : Block</p></span></div></foreignObject></g><g class="label" style="" transform="translate(0,10.5)"><foreignObject width="221.203125" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 313px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel markdown-node-label" style=""><p>+CreateStartingBlock() : : Block</p></span></div></foreignObject></g><g class="label" style="" transform="translate(0,31.5)"><foreignObject width="216.3125" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 307px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel markdown-node-label" style=""><p>+CreateRandomBlock() : : Block</p></span></div></foreignObject></g></g><g class="divider neo-line" style=""><path d="M-174.125 -43.5 C-69.75398579278747 -43.49970029859524, 34.61702841442505 -43.49940059719049, 174.125 -43.499 M-174.125 -43.5 C-63.82975690051276 -43.49968328716985, 46.465486198974475 -43.4993665743397, 174.125 -43.499" stroke="#28253D" stroke-width="1.3" fill="none" style=""/></g><g class="divider neo-line" style=""><path d="M-174.125 1.5 C-36.77368088892942 1.500394404362128, 100.57763822214116 1.5007888087242558, 174.125 1.501 M-174.125 1.5 C-96.29260412935994 1.5002234957526794, -18.460208258719888 1.5004469915053589, 174.125 1.501" stroke="#28253D" stroke-width="1.3" fill="none" style=""/></g></g><g class="node default" id="classId-NextBlockController-40" data-id="NextBlockController" data-node="true" data-et="node" data-look="neo" transform="translate(534.69921875, 149)"><g class="basic label-container outer-path"><path d="M-172.19921875 -141 L172.19921875 -141 L172.19921875 141 L-172.19921875 141" stroke="none" stroke-width="0" fill="#ffffff" style=""/><path d="M-172.19921875 -141 C-46.952145083122076 -141, 78.29492858375585 -141, 172.19921875 -141 M-172.19921875 -141 C-91.26274782003647 -141, -10.326276890072933 -141, 172.19921875 -141 M172.19921875 -141 C172.19921875 -53.16406729812893, 172.19921875 34.671865403742146, 172.19921875 141 M172.19921875 -141 C172.19921875 -48.396796880458226, 172.19921875 44.20640623908355, 172.19921875 141 M172.19921875 141 C53.621506053255075 141, -64.95620664348985 141, -172.19921875 141 M172.19921875 141 C77.88127847470675 141, -16.436661800586506 141, -172.19921875 141 M-172.19921875 141 C-172.19921875 55.155442340514085, -172.19921875 -30.68911531897183, -172.19921875 -141 M-172.19921875 141 C-172.19921875 76.90595246742333, -172.19921875 12.811904934846666, -172.19921875 -141" stroke="#28253D" stroke-width="1.3" fill="none" style=""/></g><g class="annotation-group text" transform="translate(0, -117)"/><g class="label-group text" transform="translate(-70.2109375, -117)"><g class="label" style="font-weight: bolder" transform="translate(0,-10.5)"><foreignObject width="140.421875" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 210px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel markdown-node-label" style=""><p>NextBlockController</p></span></div></foreignObject></g></g><g class="members-group text" transform="translate(-160.19921875, -72)"><g class="label" style="" transform="translate(0,-10.5)"><foreignObject width="198.109375" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 286px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel markdown-node-label" style=""><p>-BlockFactory _blockFactory</p></span></div></foreignObject></g><g class="label" style="" transform="translate(0,10.5)"><foreignObject width="143.921875" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 224px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel markdown-node-label" style=""><p>-Block _currentBlock</p></span></div></foreignObject></g><g class="label" style="" transform="translate(0,31.5)"><foreignObject width="124.609375" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 202px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel markdown-node-label" style=""><p>-Block _nextBlock</p></span></div></foreignObject></g><g class="label" style="" transform="translate(0,52.5)"><foreignObject width="250.1875" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 346px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel markdown-node-label" style=""><p>-BlockGenerationSettings _settings</p></span></div></foreignObject></g><g class="label" style="" transform="translate(0,73.5)"><foreignObject width="148.546875" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 230px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel markdown-node-label" style=""><p>+CurrentBlock : Block</p></span></div></foreignObject></g><g class="label" style="" transform="translate(0,94.5)"><foreignObject width="128.53125" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 207px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel markdown-node-label" style=""><p>+NextBlock : Block</p></span></div></foreignObject></g></g><g class="methods-group text" transform="translate(-160.19921875, 78)"><g class="label" style="" transform="translate(0,-10.5)"><foreignObject width="115.78125" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 192px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel markdown-node-label" style=""><p>+GenerateNext()</p></span></div></foreignObject></g><g class="label" style="" transform="translate(0,10.5)"><foreignObject width="156.109375" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 238px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel markdown-node-label" style=""><p>+GetCurrent() : : Block</p></span></div></foreignObject></g><g class="label" style="" transform="translate(0,31.5)"><foreignObject width="106.125" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 181px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel markdown-node-label" style=""><p>+PrepareNext()</p></span></div></foreignObject></g></g><g class="divider neo-line" style=""><path d="M-172.19921875 -96 C-77.91475803304596 -95.99972623435401, 16.369702683908088 -95.99945246870804, 172.19921875 -95.999 M-172.19921875 -96 C-38.16123960305271 -95.99961080549575, 95.87673954389459 -95.9992216109915, 172.19921875 -95.999" stroke="#28253D" stroke-width="1.3" fill="none" style=""/></g><g class="divider neo-line" style=""><path d="M-172.19921875 54 C-40.10347358258318 54.000383555007176, 91.99227158483365 54.00076711001435, 172.19921875 54.001 M-172.19921875 54 C-90.14061589381643 54.00023826647836, -8.082013037632862 54.00047653295672, 172.19921875 54.001" stroke="#28253D" stroke-width="1.3" fill="none" style=""/></g></g><g class="node default" id="classId-BlockGenerationSettings-41" data-id="BlockGenerationSettings" data-node="true" data-et="node" data-look="neo" transform="translate(578.7265625, 428.5)"><g class="basic label-container outer-path"><path d="M-172.4765625 -88.5 L172.4765625 -88.5 L172.4765625 88.5 L-172.4765625 88.5" stroke="none" stroke-width="0" fill="#ffffff" style=""/><path d="M-172.4765625 -88.5 C-42.07065015257666 -88.5, 88.33526219484668 -88.5, 172.4765625 -88.5 M-172.4765625 -88.5 C-79.87541620934253 -88.5, 12.725730081314936 -88.5, 172.4765625 -88.5 M172.4765625 -88.5 C172.4765625 -22.33279509471913, 172.4765625 43.83440981056174, 172.4765625 88.5 M172.4765625 -88.5 C172.4765625 -37.03802047672448, 172.4765625 14.423959046551033, 172.4765625 88.5 M172.4765625 88.5 C75.00873815012686 88.5, -22.45908619974628 88.5, -172.4765625 88.5 M172.4765625 88.5 C64.74834239173418 88.5, -42.97987771653163 88.5, -172.4765625 88.5 M-172.4765625 88.5 C-172.4765625 36.3055802515193, -172.4765625 -15.888839496961396, -172.4765625 -88.5 M-172.4765625 88.5 C-172.4765625 47.701459692560476, -172.4765625 6.902919385120953, -172.4765625 -88.5" stroke="#28253D" stroke-width="1.3" fill="none" style=""/></g><g class="annotation-group text" transform="translate(0, -64.5)"/><g class="label-group text" transform="translate(-86.453125, -64.5)"><g class="label" style="font-weight: bolder" transform="translate(0,-10.5)"><foreignObject width="172.90625" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 248px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel markdown-node-label" style=""><p>BlockGenerationSettings</p></span></div></foreignObject></g></g><g class="members-group text" transform="translate(-160.4765625, -19.5)"><g class="label" style="" transform="translate(0,-10.5)"><foreignObject width="191.8125" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 279px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel markdown-node-label" style=""><p>-float[] _valueProbabilities</p></span></div></foreignObject></g><g class="label" style="" transform="translate(0,10.5)"><foreignObject width="187.609375" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 274px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel markdown-node-label" style=""><p>-float[] _seedProbabilities</p></span></div></foreignObject></g></g><g class="methods-group text" transform="translate(-160.4765625, 46.5)"><g class="label" style="" transform="translate(0,-10.5)"><foreignObject width="234.5" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 328px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel markdown-node-label" style=""><p>+GetRandomValue() : : BlockValue</p></span></div></foreignObject></g><g class="label" style="" transform="translate(0,10.5)"><foreignObject width="224.28125" height="21"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 316px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel markdown-node-label" style=""><p>+GetRandomSeed() : : BlockSeed</p></span></div></foreignObject></g></g><g class="divider neo-line" style=""><path d="M-172.4765625 -43.5 C-74.27310131104454 -43.499715313606195, 23.930359877910917 -43.499430627212384, 172.4765625 -43.499 M-172.4765625 -43.5 C-60.642258428555536 -43.499675798547784, 51.19204564288893 -43.49935159709557, 172.4765625 -43.499" stroke="#28253D" stroke-width="1.3" fill="none" style=""/></g><g class="divider neo-line" style=""><path d="M-172.4765625 22.5 C-99.46927978654507 22.5002116440682, -26.461997073090146 22.5004232881364, 172.4765625 22.501 M-172.4765625 22.5 C-92.21442982586727 22.50023267547634, -11.952297151734541 22.50046535095268, 172.4765625 22.501" stroke="#28253D" stroke-width="1.3" fill="none" style=""/></g></g></g></g></g><defs><filter id="drop-shadow" height="130%" width="130%"><feDropShadow dx="4" dy="4" stdDeviation="0" flood-opacity="0.06" flood-color="#000000"/></filter></defs><defs><filter id="drop-shadow-small" height="150%" width="150%"><feDropShadow dx="2" dy="2" stdDeviation="0" flood-opacity="0.06" flood-color="#000000"/></filter></defs></svg>