# Block System - Class Diagram

```mermaid
classDiagram
    class Block {
        -BlockValue _value
        -BlockSeed _seed
        -BlockVisual _visual
        +Value : BlockValue
        +Seed : BlockSeed
        +Visual : BlockVisual
        +Init(BlockValue, BlockSeed)
        +SetVisual()
        +Destroy()
    }

    class BlockValue {
        <<enumeration>>
        Ace
        Two
        Three
        Four
        Five
        Six
        Seven
        Eight
        Nine
        Ten
        Jack
        Queen
        King
    }

    class BlockSeed {
        <<enumeration>>
        Hearts
        Diamonds
        Clubs
        Spades
    }

    class BlockVisual {
        -Image _image
        -Sprite[] _sprites
        +SetSprite(BlockValue, BlockSeed)
        +PlaySpawnAnimation()
        +PlayMergeAnimation()
        +PlayDestroyAnimation()
    }

    class BlockFactory {
        -Block _blockPrefab
        +Create(BlockValue, BlockSeed) : Block
        +CreateStartingBlock() : Block
        +CreateRandomBlock() : Block
    }

    class NextBlockController {
        -BlockFactory _blockFactory
        -Block _currentBlock
        -Block _nextBlock
        -BlockGenerationSettings _settings
        +CurrentBlock : Block
        +NextBlock : Block
        +GenerateNext()
        +GetCurrent() : Block
        +PrepareNext()
    }

    class BlockGenerationSettings {
        -float[] _valueProbabilities
        -float[] _seedProbabilities
        +GetRandomValue() : BlockValue
        +GetRandomSeed() : BlockSeed
    }

    Block --> BlockValue
    Block --> BlockSeed
    Block --> BlockVisual
    BlockFactory --> Block
    NextBlockController --> BlockFactory
    NextBlockController --> Block
    NextBlockController --> BlockGenerationSettings
```