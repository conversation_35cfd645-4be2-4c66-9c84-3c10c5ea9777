# Merge Process - Activity Diagram

```mermaid
flowchart TD
    Start([Start Merge Process]) --> CheckBoard{Scan Board for Mergeable Groups}
    
    CheckBoard --> HasGroups{Groups Found?}
    
    HasGroups -->|No| EndMerge([End Merge Process])
    HasGroups -->|Yes| ProcessGroup[Process First Group]
    
    ProcessGroup --> TremorAnim[Play Tremor Animation]
    TremorAnim --> MoveToCenter[Move Blocks to Center]
    MoveToCenter --> ScaleDown[Scale Down Blocks]
    ScaleDown --> CreateNew[Create New Block with Next Value]
    CreateNew --> ScaleUp[Scale Up New Block]
    ScaleUp --> UpdateScore[Update Score]
    
    UpdateScore --> CheckFoundation{Can Attach to Foundation?}
    CheckFoundation -->|Yes| AttachToFoundation[Attach to Foundation]
    CheckFoundation -->|No| PlaceOnBoard[Place on Board]
    
    AttachToFoundation --> CheckVictory{All Foundations Complete?}
    PlaceOnBoard --> CheckVictory
    
    CheckVictory -->|Yes| Victory([Victory State])
    CheckVictory -->|No| MoreGroups{More Groups to Process?}
    
    MoreGroups -->|Yes| ProcessGroup
    MoreGroups -->|No| ApplyGravity[Apply Gravity]
    
    ApplyGravity --> CheckBoard
    
    Victory --> EndGame([End Game])
    EndMerge --> NextState([Continue to Next State])
    
    style Start fill:#e8f5e8
    style Victory fill:#e1f5fe
    style EndGame fill:#ffebee
    style EndMerge fill:#f3e5f5
```