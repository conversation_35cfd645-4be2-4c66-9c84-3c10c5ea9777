# Merge Solitaire - Complete Project Documentation

## Table of Contents
1. [Project Overview](#project-overview)
2. [Game Design](#game-design)
3. [Technical Architecture](#technical-architecture)
4. [Core Systems](#core-systems)
5. [Development Tasks](#development-tasks)
6. [Code Structure](#code-structure)
7. [Performance Considerations](#performance-considerations)
8. [Development Guidelines](#development-guidelines)
9. [API Reference](#api-reference)
10. [Troubleshooting](#troubleshooting)

---

## Project Overview

### Introduction
**Merge Solitaire** is an innovative Unity-based puzzle game that combines classic Klondike solitaire mechanics with modern merge gameplay. The project successfully meets all of Whatwapp's key performance indicators (KPIs) and is positioned for further development and enhancement.

### Technical Specifications
- **Unity Version**: 2022.3.47f1
- **Target Platform**: Mobile (iOS/Android)
- **Architecture Pattern**: State Machine with Modular Components
- **Animation Framework**: DOTween
- **Assembly Structure**: Modular with clear dependencies

### Key Features
- **Merge Mechanics**: Combine identical blocks to progress through card values
- **Solitaire Integration**: Build foundation piles from Ace to King
- **Strategic Gameplay**: Tactical placement with gravity-based movement
- **Visual Feedback**: Comprehensive animation system with DOTween
- **Persistent Progress**: Score tracking with high score persistence

---

## Game Design

### Core Gameplay Loop
1. **Block Generation**: System generates blocks with strategic probability distribution
2. **Player Placement**: Tap-to-place mechanics with column-based input
3. **Gravity Application**: Blocks fall to lowest available positions
4. **Merge Detection**: Connected identical blocks automatically merge
5. **Foundation Building**: Merged blocks attach to appropriate foundation piles
6. **Victory/Defeat**: Game ends when foundations complete or no moves remain

### Game Rules

#### Block Placement
- Players tap on any column to place the current block
- Blocks fall to the lowest available cell in the selected column
- Invalid placements (full columns) provide visual feedback

#### Merge Mechanics
- **Trigger Condition**: Two or more orthogonally adjacent identical blocks
- **Merge Process**: Blocks combine into next sequential value (Ace→Two→Three...→King)
- **Animation Sequence**: Tremor → Movement → Scale Down → Create New → Scale Up
- **Foundation Integration**: Merged blocks automatically attach to foundations when possible

#### Victory Conditions
- **Primary Goal**: Complete all four foundation piles (Ace to King)
- **Foundation Rules**: Sequential building by suit (Hearts, Diamonds, Clubs, Spades)
- **Automatic Attachment**: System handles foundation placement during merges

#### Defeat Conditions
- **Board Full**: No valid placement positions remain
- **No Merges**: Board state prevents further progress

### Scoring System
- **Base Points**: Points awarded per successful merge
- **Multiplier System**: Consecutive merges increase score multiplier
- **Foundation Bonus**: Additional points for foundation completions
- **High Score Tracking**: Persistent best score using PlayerPrefs

---

## Technical Architecture

### Design Patterns

#### State Machine Pattern
The core game flow uses a finite state machine for clean state management:

```csharp
public class StateMachine : MonoBehaviour
{
    private IState _currentState;
    private Dictionary<Type, IState> _states;
    
    public void ChangeState<T>() where T : IState
    {
        _currentState?.Exit();
        _currentState = _states[typeof(T)];
        _currentState.Enter();
    }
}
```

#### Factory Pattern
Block creation uses the factory pattern for consistent object instantiation:

```csharp
public class BlockFactory : MonoBehaviour
{
    public Block Create(BlockValue value, BlockSeed seed)
    {
        var block = Instantiate(_blockPrefab, transform);
        block.Init(value, seed);
        return block;
    }
}
```

#### Observer Pattern
Score updates and UI synchronization use observer-like patterns:

```csharp
public int Score
{
    get => _score;
    set
    {
        _score = value;
        if (_score > _highScore)
        {
            _highScore = _score;
            PlayerPrefs.SetInt(Consts.PREFS_HIGHSCORE, _highScore);
        }
        _scoreBox.SetScore(_score);
    }
}
```

### Assembly Structure

#### Whatwapp.MergeSolitaire.Game
**Primary game assembly containing:**
- Game states and state machine
- Core gameplay controllers
- Block and board systems
- UI controllers specific to the game

#### Whatwapp.Core.UI
**Reusable UI framework:**
- Base panel system (`APanel`)
- Common UI components
- Animation utilities

#### Whatwapp.Core.Utils
**Utility classes and extensions:**
- Helper methods
- Extension classes
- Executable patterns

#### External Dependencies
- **DOTween**: Animation framework
- **Unity UI**: User interface system
- **TextMeshPro**: Advanced text rendering

---

## Core Systems

### Game Controller System

#### GameController.cs
**Central orchestrator managing:**
- Game state coordination
- Score management and persistence
- Pause/resume functionality
- High score tracking

```csharp
public class GameController : MonoBehaviour
{
    [SerializeField] private StateMachine _stateMachine;
    [SerializeField] private ScoreBox _scoreBox;
    
    private int _score = 0;
    private int _highScore = 0;
    private bool _isPaused = false;
    
    public void StartGame() => _stateMachine.ChangeState<GenerateLevelState>();
    public void PauseGame() => _isPaused = true;
    public void ResumeGame() => _isPaused = false;
}
```

### State Machine System

#### Game States Overview

**GenerateLevelState**
- Initializes board and grid
- Places starting blocks
- Sets up camera targets
- Transitions to ExtractBlockState

**ExtractBlockState**
- Generates next block using probability system
- Updates UI displays
- Prepares block for placement
- Transitions to PlayBlockState

**PlayBlockState**
- Handles player input and touch detection
- Validates placement positions
- Shows placement previews
- Checks for game over conditions
- Transitions to MoveBlocksState on valid placement

**MoveBlocksState**
- Applies gravity to all blocks
- Animates block movement with DOTween
- Updates board state after movement
- Transitions to MergeBlocksState

**MergeBlocksState**
- Detects mergeable block groups using flood-fill algorithm
- Animates merge sequences
- Creates new blocks with incremented values
- Handles foundation attachments
- Updates score and checks victory conditions
- Transitions to ExtractBlockState or VictoryState

**GameOverState / VictoryState**
- Handles end-game scenarios
- Displays appropriate UI panels
- Manages game restart options

### Block System

#### Block Architecture
```csharp
public class Block : MonoBehaviour
{
    [SerializeField] private BlockValue _value;
    [SerializeField] private BlockSeed _seed;
    [SerializeField] private BlockVisual _visual;
    
    public BlockValue Value => _value;
    public BlockSeed Seed => _seed;
    
    public void Init(BlockValue value, BlockSeed seed)
    {
        _value = value;
        _seed = seed;
        _visual.SetSprite(value, seed);
    }
}
```

#### Block Generation System
```csharp
public class NextBlockController : MonoBehaviour
{
    private BlockFactory _blockFactory;
    private Block _currentBlock;
    private Block _nextBlock;
    private BlockGenerationSettings _settings;
    
    public void GenerateNext()
    {
        var value = _settings.GetRandomValue();
        var seed = _settings.GetRandomSeed();
        _nextBlock = _blockFactory.Create(value, seed);
    }
}
```

### Board System

#### Board Structure
```csharp
public class Board : MonoBehaviour
{
    private Cell[,] _cells;
    private int _width = 7;
    private int _height = 10;
    
    public Cell GetCell(int x, int y) => _cells[x, y];
    public Cell[] GetColumn(int column) => /* Implementation */;
    public bool IsColumnFull(int column) => /* Implementation */;
    public Cell GetTopEmptyCell(int column) => /* Implementation */;
}
```

#### Cell Management
```csharp
public class Cell : MonoBehaviour
{
    private Block _block;
    private Vector2Int _position;
    
    public bool IsEmpty => _block == null;
    public bool HasBlock => _block != null;
    
    public void SetBlock(Block block)
    {
        _block = block;
        block.transform.position = transform.position;
    }
    
    public void RemoveBlock()
    {
        _block = null;
    }
}
```

### Merge System

#### Merge Detection Algorithm
The system uses a flood-fill (BFS) algorithm to detect connected groups:

```csharp
private List<Cell> GetMergeableCells(Cell startCell, bool[,] visited)
{
    var mergeableCells = new List<Cell>();
    var targetValue = startCell.Block.Value;
    var queue = new Queue<Cell>();
    
    queue.Enqueue(startCell);
    visited[startCell.Position.x, startCell.Position.y] = true;
    
    while (queue.Count > 0)
    {
        var currentCell = queue.Dequeue();
        mergeableCells.Add(currentCell);
        
        // Check all four orthogonal directions
        foreach (var direction in _directions)
        {
            var neighborPos = currentCell.Position + direction;
            
            if (IsValidPosition(neighborPos) && !visited[neighborPos.x, neighborPos.y])
            {
                var neighborCell = _board.GetCell(neighborPos.x, neighborPos.y);
                
                if (neighborCell.HasBlock && 
                    neighborCell.Block.Value == targetValue)
                {
                    visited[neighborPos.x, neighborPos.y] = true;
                    queue.Enqueue(neighborCell);
                }
            }
        }
    }
    
    return mergeableCells.Count >= 2 ? mergeableCells : new List<Cell>();
}
```

#### Merge Animation Sequence
```csharp
private Sequence AnimateMerge(List<Cell> cells)
{
    var sequence = DOTween.Sequence();
    
    // 1. Tremor effect
    sequence.Append(CreateTremorAnimation(cells));
    
    // 2. Move to center
    sequence.Append(CreateMoveToCenter(cells));
    
    // 3. Scale down
    sequence.Append(CreateScaleDown(cells));
    
    // 4. Create new block
    sequence.AppendCallback(() => CreateMergedBlock(cells));
    
    // 5. Scale up new block
    sequence.Append(CreateScaleUp(newBlock));
    
    return sequence;
}
```

### Foundation System

#### Foundation Management
```csharp
public class FoundationsController : MonoBehaviour
{
    private Foundation[] _foundations = new Foundation[4];
    
    public bool CanAttachBlock(Block block)
    {
        var foundation = GetFoundationForSeed(block.Seed);
        return foundation.CanAttach(block);
    }
    
    public bool AttachBlock(Block block)
    {
        var foundation = GetFoundationForSeed(block.Seed);
        if (foundation.CanAttach(block))
        {
            foundation.AttachBlock(block);
            return true;
        }
        return false;
    }
    
    public bool IsComplete()
    {
        return _foundations.All(f => f.IsComplete);
    }
}
```

#### Individual Foundation Logic
```csharp
public class Foundation : MonoBehaviour
{
    private BlockSeed _seed;
    private List<Block> _blocks = new List<Block>();
    
    public bool CanAttach(Block block)
    {
        if (block.Seed != _seed) return false;
        
        var expectedValue = GetNextExpectedValue();
        return block.Value == expectedValue;
    }
    
    private BlockValue GetNextExpectedValue()
    {
        return _blocks.Count == 0 ? BlockValue.Ace : 
               (BlockValue)((int)_blocks.Last().Value + 1);
    }
}
```

### Animation System

#### Animation Settings Configuration
```csharp
[CreateAssetMenu(menuName = "MergeSolitaire/Settings/Animations")]
public class AnimationSettings : ScriptableObject
{
    [Header("Block Movement")]
    public float blockMoveDuration = 0.3f;
    public Ease blockMoveEase = Ease.OutQuart;
    
    [Header("Merge Effects")]
    public float tremorDuration = 0.25f;
    public float tremorStrength = 0.1f;
    public float mergeScaleDuration = 0.2f;
    
    [Header("Spawn Effects")]
    public float spawnScaleDuration = 0.3f;
    public Vector3 spawnScale = Vector3.one * 1.2f;
}
```

#### DOTween Integration
```csharp
public class BlockVisual : MonoBehaviour
{
    public void PlaySpawnAnimation()
    {
        transform.localScale = Vector3.zero;
        transform.DOScale(Vector3.one, _animationSettings.spawnScaleDuration)
                 .SetEase(Ease.OutBack);
    }
    
    public void PlayMergeAnimation(Vector3 targetPosition)
    {
        var sequence = DOTween.Sequence();
        sequence.Append(transform.DOMove(targetPosition, 0.3f));
        sequence.Join(transform.DOScale(Vector3.zero, 0.3f));
        sequence.OnComplete(() => Destroy(gameObject));
    }
}
```

---

## Development Tasks

### Priority 1: Bomb Block Implementation

#### Requirements
- New block type with explosive mechanics
- Destroys adjacent blocks upon placement
- Integrates with existing merge system
- Provides impactful visual feedback

#### Implementation Strategy
```csharp
public enum BlockType
{
    Normal,
    Bomb
}

public class BombBlock : Block
{
    public override void OnPlacementComplete()
    {
        StartCoroutine(ExplodeAfterDelay());
    }
    
    private IEnumerator ExplodeAfterDelay()
    {
        yield return new WaitForSeconds(0.5f);
        
        var adjacentCells = GetAdjacentCells();
        foreach (var cell in adjacentCells)
        {
            if (cell.HasBlock)
            {
                DestroyBlock(cell.Block);
            }
        }
        
        PlayExplosionEffect();
        Destroy(gameObject);
    }
}
```

#### Visual Effects Requirements
- Explosion particle system
- Screen shake effect
- Sound effects integration
- Smooth destruction animations

### Priority 2: Game Effects Enhancement

#### Core Animation Improvements
1. **Cell Spawn Animations**
   - Staggered appearance with scale/fade effects
   - Bounce or elastic easing for impact

2. **Movement Transitions**
   - Smooth gravity-based falling
   - Anticipation and follow-through
   - Trail effects for fast movement

3. **Merge Animations**
   - Enhanced tremor effects
   - Particle burst on merge
   - Color-coded feedback by value

4. **Score Feedback**
   - Floating score text with physics
   - Score counter animation with easing
   - Milestone celebration effects

#### Implementation Examples
```csharp
public class EnhancedScoreDisplay : MonoBehaviour
{
    public void ShowFloatingScore(int points, Vector3 worldPosition)
    {
        var floatingText = Instantiate(_floatingTextPrefab);
        floatingText.transform.position = worldPosition;
        floatingText.GetComponent<TextMeshPro>().text = $"+{points}";
        
        var sequence = DOTween.Sequence();
        sequence.Append(floatingText.transform.DOMoveY(worldPosition.y + 2f, 1f));
        sequence.Join(floatingText.GetComponent<CanvasGroup>().DOFade(0f, 1f));
        sequence.OnComplete(() => Destroy(floatingText));
    }
}
```

### Priority 3: End Game Panel Enhancement

#### Victory Panel Features
- Animated score reveal
- Star rating system
- Particle celebration effects
- Social sharing integration

#### Defeat Panel Features
- Encouraging messaging
- Restart button with smooth transition
- Progress indication
- Hint system integration

### Priority 4: Scene Transitions

#### Additive Scene Loading
```csharp
public class SceneTransitionManager : MonoBehaviour
{
    public void LoadGameScene()
    {
        StartCoroutine(LoadSceneAsync("GameScene"));
    }
    
    private IEnumerator LoadSceneAsync(string sceneName)
    {
        // Fade out current scene
        yield return FadeOut();
        
        // Load new scene additively
        var asyncLoad = SceneManager.LoadSceneAsync(sceneName, LoadSceneMode.Additive);
        yield return asyncLoad;
        
        // Fade in new scene
        yield return FadeIn();
    }
}
```

### Priority 5: Asset Optimization

#### Sprite Atlas Configuration
- Group sprites by usage frequency
- Optimize texture sizes for target platforms
- Implement proper compression settings

#### Prefab Organization
- Modular prefab structure
- Nested prefab variants
- Consistent naming conventions

---

## Code Structure

### Directory Organization
```
Scripts/
├── Whatwapp/
│   ├── MergeSolitaire/
│   │   └── Game/
│   │       ├── GameStates/
│   │       ├── Controllers/
│   │       ├── Settings/
│   │       └── UI/
│   └── Core/
│       ├── UI/
│       ├── Utils/
│       └── Extensions/
├── Settings/
└── Prefabs/
```

### Naming Conventions

#### Classes
- **Controllers**: `GameController`, `NextBlockController`
- **States**: `PlayBlockState`, `MergeBlocksState`
- **Settings**: `AnimationSettings`, `BlockGenerationSettings`
- **UI**: `ScoreBox`, `GamePanel`

#### Methods
- **Public Methods**: PascalCase (`StartGame`, `PlaceBlock`)
- **Private Methods**: PascalCase (`HandleInput`, `ValidatePlacement`)
- **Coroutines**: Descriptive with "Coroutine" or action (`MoveBlocksCoroutine`)

#### Variables
- **Public Fields**: PascalCase (`Score`, `HighScore`)
- **Private Fields**: Underscore prefix (`_score`, `_stateMachine`)
- **Serialized Fields**: Underscore prefix (`_blockPrefab`, `_animationSettings`)

### Code Quality Standards

#### SOLID Principles
- **Single Responsibility**: Each class has one clear purpose
- **Open/Closed**: Extensible through inheritance and composition
- **Liskov Substitution**: Proper interface implementation
- **Interface Segregation**: Focused interfaces like `IState`
- **Dependency Inversion**: Dependency injection through serialized fields

#### Performance Guidelines
- Minimize allocations in Update loops
- Use object pooling for frequently created objects
- Cache component references
- Prefer structs for simple data containers

---

## Performance Considerations

### Memory Management

#### Object Pooling Strategy
```csharp
public class BlockPool : MonoBehaviour
{
    private Queue<Block> _availableBlocks = new Queue<Block>();
    private List<Block> _activeBlocks = new List<Block>();
    
    public Block GetBlock()
    {
        Block block;
        if (_availableBlocks.Count > 0)
        {
            block = _availableBlocks.Dequeue();
        }
        else
        {
            block = Instantiate(_blockPrefab);
        }
        
        _activeBlocks.Add(block);
        return block;
    }
    
    public void ReturnBlock(Block block)
    {
        _activeBlocks.Remove(block);
        _availableBlocks.Enqueue(block);
        block.gameObject.SetActive(false);
    }
}
```

#### Garbage Collection Optimization
- Avoid string concatenation in loops
- Use StringBuilder for complex string operations
- Cache frequently accessed components
- Minimize boxing/unboxing operations

### Algorithm Efficiency

#### Merge Detection Optimization
- **Time Complexity**: O(n) where n is the number of cells
- **Space Complexity**: O(n) for visited array and queue
- **Optimization**: Early termination when no merges possible

#### Board State Management
- **Efficient Column Access**: Pre-computed column arrays
- **Spatial Queries**: Cached neighbor relationships
- **State Validation**: Lazy evaluation of game over conditions

### Rendering Performance

#### UI Optimization
- Canvas group management for complex UI
- Efficient text rendering with TextMeshPro
- Proper use of UI object pooling

#### Animation Performance
- DOTween sequence reuse
- Efficient tween targeting
- Proper cleanup of completed animations

---

## Development Guidelines

### Code Review Checklist

#### Architecture
- [ ] Follows established state machine pattern
- [ ] Proper separation of concerns
- [ ] Consistent with existing naming conventions
- [ ] Appropriate use of design patterns

#### Performance
- [ ] No unnecessary allocations in hot paths
- [ ] Proper component caching
- [ ] Efficient algorithm implementation
- [ ] Memory leak prevention

#### Maintainability
- [ ] Clear and descriptive naming
- [ ] Appropriate code comments
- [ ] Modular and testable design
- [ ] Proper error handling

### Testing Strategy

#### Unit Testing
```csharp
[Test]
public void MergeDetection_FindsConnectedBlocks()
{
    // Arrange
    var board = CreateTestBoard();
    var mergeState = new MergeBlocksState();
    
    // Act
    var mergeableGroups = mergeState.GetAllMergableCells();
    
    // Assert
    Assert.AreEqual(1, mergeableGroups.Count);
    Assert.AreEqual(3, mergeableGroups[0].Count);
}
```

#### Integration Testing
- State transition validation
- UI interaction testing
- Animation sequence verification
- Save/load functionality testing

### Documentation Standards

#### Code Documentation
```csharp
/// <summary>
/// Detects and returns all groups of mergeable blocks on the board.
/// Uses flood-fill algorithm to find connected components.
/// </summary>
/// <returns>List of cell groups that can be merged</returns>
public List<List<Cell>> GetAllMergableCells()
{
    // Implementation
}
```

#### API Documentation
- Public method documentation
- Parameter descriptions
- Return value specifications
- Usage examples

---

## API Reference

### Core Classes

#### GameController
```csharp
public class GameController : MonoBehaviour
{
    // Properties
    public int Score { get; set; }
    public int HighScore { get; }
    public bool IsPaused { get; }
    
    // Methods
    public void StartGame();
    public void PauseGame();
    public void ResumeGame();
    public void EndGame();
}
```

#### StateMachine
```csharp
public class StateMachine : MonoBehaviour
{
    public void ChangeState<T>() where T : IState;
    public T GetState<T>() where T : IState;
    public void Update();
    public void FixedUpdate();
}
```

#### Block
```csharp
public class Block : MonoBehaviour
{
    public BlockValue Value { get; }
    public BlockSeed Seed { get; }
    public BlockVisual Visual { get; }
    
    public void Init(BlockValue value, BlockSeed seed);
    public void SetVisual();
    public void Destroy();
}
```

#### Board
```csharp
public class Board : MonoBehaviour
{
    public int Width { get; }
    public int Height { get; }
    public Cell[] Cells { get; }
    
    public Cell GetCell(int x, int y);
    public Cell[] GetColumn(int column);
    public bool IsValidPosition(int x, int y);
    public bool IsColumnFull(int column);
    public Cell GetTopEmptyCell(int column);
}
```

### Enumerations

#### BlockValue
```csharp
public enum BlockValue
{
    Ace = 1,
    Two = 2,
    Three = 3,
    Four = 4,
    Five = 5,
    Six = 6,
    Seven = 7,
    Eight = 8,
    Nine = 9,
    Ten = 10,
    Jack = 11,
    Queen = 12,
    King = 13
}
```

#### BlockSeed
```csharp
public enum BlockSeed
{
    Hearts,
    Diamonds,
    Clubs,
    Spades
}
```

### Events and Delegates

#### Game Events
```csharp
public static class GameEvents
{
    public static event Action<int> OnScoreChanged;
    public static event Action<Block> OnBlockPlaced;
    public static event Action<List<Cell>> OnBlocksMerged;
    public static event Action OnGameWon;
    public static event Action OnGameLost;
}
```

---

## Troubleshooting

### Common Issues

#### State Machine Problems
**Issue**: States not transitioning properly
**Solution**: Verify state registration in StateMachine initialization
```csharp
private void InitializeStates()
{
    _states.Add(typeof(GenerateLevelState), _generateLevelState);
    _states.Add(typeof(PlayBlockState), _playBlockState);
    // ... register all states
}
```

#### Animation Issues
**Issue**: DOTween animations not playing
**Solution**: Ensure DOTween is properly initialized
```csharp
private void Awake()
{
    DOTween.Init();
}
```

#### Performance Problems
**Issue**: Frame rate drops during merges
**Solution**: Optimize merge detection algorithm and reduce particle effects

#### UI Responsiveness
**Issue**: Touch input not registering
**Solution**: Check Canvas raycast settings and UI layer configuration

### Debug Tools

#### State Visualization
```csharp
#if UNITY_EDITOR
private void OnGUI()
{
    GUILayout.Label($"Current State: {_stateMachine.CurrentState.GetType().Name}");
    GUILayout.Label($"Score: {Score}");
    GUILayout.Label($"Blocks on Board: {_board.GetActiveBlockCount()}");
}
#endif
```

#### Performance Monitoring
```csharp
public class PerformanceMonitor : MonoBehaviour
{
    private void Update()
    {
        if (Time.frameCount % 60 == 0) // Check every 60 frames
        {
            var fps = 1.0f / Time.deltaTime;
            Debug.Log($"FPS: {fps:F1}");
        }
    }
}
```

### Best Practices

#### Error Handling
```csharp
public bool TryPlaceBlock(Block block, int column)
{
    try
    {
        if (!IsValidColumn(column))
        {
            Debug.LogWarning($"Invalid column: {column}");
            return false;
        }
        
        var cell = GetTopEmptyCell(column);
        if (cell == null)
        {
            Debug.LogWarning($"Column {column} is full");
            return false;
        }
        
        cell.SetBlock(block);
        return true;
    }
    catch (System.Exception e)
    {
        Debug.LogError($"Failed to place block: {e.Message}");
        return false;
    }
}
```

#### Logging Strategy
```csharp
public static class GameLogger
{
    public static void LogGameEvent(string eventName, params object[] parameters)
    {
        #if DEVELOPMENT_BUILD || UNITY_EDITOR
        Debug.Log($"[GAME] {eventName}: {string.Join(", ", parameters)}");
        #endif
    }
}
```

---

## Conclusion

This documentation provides a comprehensive overview of the Merge Solitaire project, covering all aspects from high-level architecture to implementation details. The modular design and clear separation of concerns make the codebase maintainable and extensible, while the established patterns provide a solid foundation for implementing the required development tasks.

For additional information, refer to the UML diagrams in the `/docs/uml/` directory and the individual component documentation within the codebase.