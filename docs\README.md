# Merge Solitaire - UML Documentation

This directory contains comprehensive UML diagrams for the Merge Solitaire project, providing visual documentation of the system architecture, design patterns, and component relationships.

## Diagram Index

### 📋 Class Diagrams
- **[Core Game Architecture](uml/class-diagram-core.md)** - Main game controller and state machine
- **[Block System](uml/class-diagram-blocks.md)** - Block creation, management, and visual representation
- **[Board System](uml/class-diagram-board.md)** - Grid, cells, and foundation management

### 🔄 Behavioral Diagrams
- **[State Machine](uml/state-machine-diagram.md)** - Game state transitions and flow
- **[Sequence Diagram](uml/sequence-diagram-placement.md)** - Block placement interaction flow
- **[Activity Diagram](uml/activity-diagram-merge.md)** - Merge process workflow

### 🏗️ Structural Diagrams
- **[Component Diagram](uml/component-diagram.md)** - System architecture overview
- **[Package Diagram](uml/package-diagram.md)** - Assembly dependencies and structure

### 👤 Use Case Diagram
- **[Use Cases](uml/use-case-diagram.md)** - Player interactions and system capabilities

## How to View Diagrams

These diagrams use Mermaid syntax and can be viewed in:
- **GitHub** - Native Mermaid support in markdown files
- **VS Code** - With Mermaid Preview extension
- **Mermaid Live Editor** - Copy/paste diagram code at [mermaid.live](https://mermaid.live)
- **Documentation Tools** - GitBook, Notion, etc. with Mermaid support

## Architecture Highlights

### 🎯 Key Design Patterns
- **State Machine Pattern** - Clean game flow management
- **Factory Pattern** - Block creation and management
- **Observer Pattern** - Score updates and UI synchronization
- **Command Pattern** - Input handling and action execution

### 🔧 Technical Features
- **Modular Architecture** - Clear separation of concerns
- **ScriptableObject Configuration** - Data-driven design
- **DOTween Integration** - Smooth animations and transitions
- **Flood Fill Algorithm** - Efficient merge detection

### 📊 Performance Considerations
- **Object Pooling** - Efficient memory management
- **Minimal Garbage Collection** - Optimized core loops
- **Efficient Algorithms** - BFS for merge detection
- **Asset Organization** - Modular assembly structure

## Development Guidelines

When extending the system:
1. Follow the established state machine pattern
2. Use ScriptableObjects for configuration
3. Implement proper separation of concerns
4. Maintain consistent naming conventions
5. Add appropriate documentation for new components

## Related Documentation
- [Project Overview](../README.md)
- [Development Tasks](../TASKS.md)
- [Technical Specifications](../TECHNICAL.md)